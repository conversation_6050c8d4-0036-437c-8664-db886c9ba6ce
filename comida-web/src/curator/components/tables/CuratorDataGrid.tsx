/**
 * Custom DataGrid component for curator domain that supports hiding selection and toolbar
 */
import { EuiDataGrid, EuiDataGridColumn } from '@elastic/eui';
import * as Sentry from '@sentry/react';
import { ReactNode, useMemo, useState } from 'react';

import { DefaultErrorMessage } from '@/components/eui/DataGrid/DefaultErrorMessage';
import { usePaginationState } from '@/components/eui/DataGrid/pagination';
import { useRowSorting, useSortingColumns } from '@/components/eui/DataGrid/sorting';

export type CellRenderFn<TValue> = (value: TValue) => ReactNode;
export type CellRenderFnMap<TData> = {
    [P in keyof TData]?: CellRenderFn<TData[P]>;
};

export type CuratorDataGridProps<TData> = {
    data: Array<TData>;
    columns: EuiDataGridColumn[];
    cellRenderFunctions: CellRenderFnMap<TData>;
    defaultVisibleColumns?: string[];
    showSkeleton?: boolean;
    hideToolbar?: boolean;
    hideSelection?: boolean;
};

export default function CuratorDataGrid<TData>({
    data,
    columns,
    cellRenderFunctions,
    defaultVisibleColumns,
    showSkeleton,
    hideToolbar = true,
    hideSelection = true,
}: CuratorDataGridProps<TData>) {
    const START_PAGE_SIZE = 25;
    const tableData = useMemo(() => {
        if (data.length <= 0 && showSkeleton) {
            return Array(START_PAGE_SIZE).fill({} as TData);
        }
        return data;
    }, [data, showSkeleton]);

    const sorting = useSortingColumns();
    const { withDataRowIndex } = useRowSorting(tableData, {
        columns: sorting.columns,
    });
    const pagination = usePaginationState(START_PAGE_SIZE);

    const [visibleColumns, setVisibleColumns] = useState(defaultVisibleColumns || columns.map(({ id }) => id));

    const renderCellValue = useMemo(() => {
        return withDataRowIndex(({ dataRowIndex, columnId: rawColumnId }: any) => {
            const columnId = rawColumnId as keyof TData;
            const rowData = tableData[dataRowIndex];
            const cellValue = rowData[columnId];

            if (showSkeleton) {
                return <div>Loading...</div>;
            }

            const cellRenderFn = cellRenderFunctions[columnId];
            if (cellRenderFn) {
                return cellRenderFn(cellValue);
            }

            return String(cellValue);
        });
    }, [showSkeleton, withDataRowIndex, tableData, cellRenderFunctions]);

    const toolbarVisibility = hideToolbar
        ? false
        : {
              showColumnSelector: false,
              showSortSelector: false,
              showKeyboardShortcuts: false,
              showFullScreenSelector: false,
          };

    return (
        <Sentry.ErrorBoundary fallback={<DefaultErrorMessage />}>
            <EuiDataGrid
                aria-label="Curator themes table"
                columnVisibility={{
                    visibleColumns,
                    setVisibleColumns,
                }}
                toolbarVisibility={toolbarVisibility}
                leadingControlColumns={hideSelection ? [] : undefined}
                renderCellValue={renderCellValue}
                sorting={sorting}
                pagination={pagination}
                rowCount={tableData.length}
                columns={columns}
            />
        </Sentry.ErrorBoundary>
    );
}
