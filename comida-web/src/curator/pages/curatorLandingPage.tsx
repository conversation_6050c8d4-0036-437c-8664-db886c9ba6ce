/**
 * Curator landing page - main entry point for curator domain.
 * Basic structure for now, will be enhanced as we develop.
 */
import {
    <PERSON>uiBadge,
    EuiBasicTable,
    EuiBasicTableColumn,
    EuiDatePicker,
    EuiDatePickerRange,
    EuiFieldSearch,
    EuiFilterButton,
    EuiFilterGroup,
    EuiFlexGroup,
    EuiFlexItem,
    EuiPage,
    EuiPageBody,
    EuiPageTemplate,
    EuiPopover,
    EuiSelectable,
    EuiSelectableOption,
    EuiSpacer,
    EuiTextBlockTruncate,
} from '@elastic/eui';
import moment from 'moment';
import React, { useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import ComidaPage from '@/components/Page/ComidaPage';
import type { ActionBarItem } from '@/curator/components';
import { ActionsBar, Sidebar, transformBreadcrumbsWithIcons, useCuratorBreadcrumbs } from '@/curator/components';

// Simple debounce utility for curator domain
const useDebounceFn = (func: (...args: any[]) => void, delay: number) => {
    let timerId: ReturnType<typeof setTimeout>;
    return (...args: any[]) => {
        clearTimeout(timerId);
        timerId = setTimeout(() => func(...args), delay);
    };
};

// Mock data for themes table - replace with real API data later
const mockThemes = [
    {
        id: 1,
        name: 'New year New Me',
        status: 'Finished',
        placement: 'Checkout',
        start_date: '01.01.25',
        end_date: '01.02.25',
        add_to_carts: '3.22%',
        impressions: '1,234',
        gm_per_click: '10.54 NOK',
        segment: 'Premium',
    },
    {
        id: 2,
        name: 'Valentines day',
        status: 'Finished',
        placement: 'Front page',
        start_date: '21.01.25',
        end_date: '15.02.25',
        add_to_carts: '1.65%',
        impressions: '34,222',
        gm_per_click: '4.89 NOK',
        segment: 'Standard',
    },
    {
        id: 3,
        name: 'Fastlawn',
        status: 'Active',
        placement: 'Search',
        start_date: '28.02.25',
        end_date: '30.03.25',
        add_to_carts: '4.09%',
        impressions: '764',
        gm_per_click: '23.67 NOK',
        segment: 'Premium',
    },
    {
        id: 4,
        name: 'Festa Italia',
        status: 'Active',
        placement: 'Front page',
        start_date: '14.02.25',
        end_date: '28.03.25',
        add_to_carts: '3.01%',
        impressions: '12,892',
        gm_per_click: '—',
        segment: 'Standard',
    },
    {
        id: 5,
        name: 'American Pancake breakfast',
        status: 'Finished',
        placement: 'Search',
        start_date: '18.01.25',
        end_date: '18.03.25',
        add_to_carts: '0.80%',
        impressions: '103',
        gm_per_click: '—',
        segment: 'Budget',
    },
    {
        id: 6,
        name: 'Local Produce',
        status: 'Active',
        placement: 'Front page',
        start_date: '01.03.25',
        end_date: '01.04.25',
        add_to_carts: '1.13%',
        impressions: '3,453',
        gm_per_click: '—',
        segment: 'Premium',
    },
    {
        id: 7,
        name: 'Fill up with fruit',
        status: 'Active',
        placement: 'Search',
        start_date: '01.03.25',
        end_date: '01.04.25',
        add_to_carts: '0.78%',
        impressions: '223',
        gm_per_click: '-45.90 NOK',
        segment: 'Standard',
    },
    {
        id: 8,
        name: 'Start your day healthy',
        status: 'Ready',
        placement: 'Front page',
        start_date: '02.04.25',
        end_date: '01.05.25',
        add_to_carts: '—',
        impressions: '—',
        gm_per_click: '—',
        segment: 'Premium',
    },
    {
        id: 9,
        name: 'Traditional Norwegian Meals',
        status: 'Everlasting',
        placement: 'Checkout',
        start_date: '02.04.25',
        end_date: '—',
        add_to_carts: '1.21%',
        impressions: '11,134',
        gm_per_click: '0.73 NOK',
        segment: 'Standard',
    },
    {
        id: 10,
        name: 'Weekend with local goods',
        status: 'Incomplete',
        placement: 'Checkout',
        start_date: '02.04.25',
        end_date: '01.05.25',
        add_to_carts: '—',
        impressions: '—',
        gm_per_click: '—',
        segment: 'Budget',
    },
];

const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
        case 'ready':
            return 'success';
        case 'active':
            return 'primary';
        case 'finished':
            return 'default';
        case 'everlasting':
            return 'accent';
        case 'incomplete':
            return 'warning';
        case 'draft':
            return 'default';
        default:
            return 'primary';
    }
};

const CuratorLandingPage: React.FC = () => {
    const navigate = useNavigate();
    const curatorBreadcrumbs = useCuratorBreadcrumbs();
    const [pageIndex, setPageIndex] = useState(0);
    const [pageSize, setPageSize] = useState(10);

    // Search and filter state
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedSegments, setSelectedSegments] = useState<string[]>([]);
    const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
    const [startDate, setStartDate] = useState<moment.Moment | null>(null);
    const [endDate, setEndDate] = useState<moment.Moment | null>(null);

    // Popover states for filters
    const [isSegmentPopoverOpen, setIsSegmentPopoverOpen] = useState(false);
    const [isStatusPopoverOpen, setIsStatusPopoverOpen] = useState(false);

    // Transform breadcrumbs to include icons if needed to match curator design for breadcrumbs
    const breadcrumbs = transformBreadcrumbsWithIcons(curatorBreadcrumbs);

    // Filter and search logic
    const filteredThemes = useMemo(() => {
        return mockThemes.filter((theme) => {
            // Search filter
            const matchesSearch = searchQuery === '' ||
                theme.name.toLowerCase().includes(searchQuery.toLowerCase());

            // Segment filter - check if theme segment is in selected segments array
            const matchesSegment = selectedSegments.length === 0 || selectedSegments.includes(theme.segment);

            // Status filter - check if theme status is in selected statuses array
            const matchesStatus = selectedStatuses.length === 0 || selectedStatuses.includes(theme.status);

            // Date filter - strict range filtering (themes must fall within selected range)
            let matchesDateRange = true;
            if (startDate || endDate) {
                // Parse theme dates from "DD.MM.YY" format to moment (assuming 20YY for years)
                const themeStartDate = moment(theme.start_date, 'DD.MM.YY');
                const themeEndDate = theme.end_date !== '—' ? moment(theme.end_date, 'DD.MM.YY') : null;

                // For strict filtering: theme must start and end within selected range
                if (startDate && themeStartDate.isBefore(startDate, 'day')) {
                    matchesDateRange = false;
                }
                if (endDate && themeEndDate && themeEndDate.isAfter(endDate, 'day')) {
                    matchesDateRange = false;
                }
                // If theme has no end date (ongoing), only check start date
                if (endDate && !themeEndDate && themeStartDate.isAfter(endDate, 'day')) {
                    matchesDateRange = false;
                }
            }

            return matchesSearch && matchesSegment && matchesStatus && matchesDateRange;
        });
    }, [searchQuery, selectedSegments, selectedStatuses, startDate, endDate]);

    // Debounced search handler
    const debouncedSearch = useDebounceFn((query: string) => {
        setSearchQuery(query);
        setPageIndex(0); // Reset to first page when searching
    }, 300);

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        debouncedSearch(e.target.value);
    };

    const items: ActionBarItem[] = [
        {
            id: 'create-theme',
            type: 'action',
            label: 'Create new theme',
            iconType: 'plusInCircle',
            onClick: () => navigate('/curator/themes/new'),
        },
    ];

    const onTableChange = ({ page }: any) => {
        if (page) {
            setPageIndex(page.index);
            setPageSize(page.size);
        }
    };

    // Define columns for the themes table
    const columns: EuiBasicTableColumn<any>[] = [
        {
            field: 'name',
            name: 'Theme',
            width: '30%',
            render: (name: string) => (
                <EuiTextBlockTruncate lines={2} style={{ width: '100%' }}>
                    {name}
                </EuiTextBlockTruncate>
            ),
            truncateText: { lines: 2 },
        },
        {
            field: 'status',
            name: 'Status',
            width: '8%',
            render: (status: string) => <EuiBadge color={getStatusColor(status)}>{status}</EuiBadge>,
        },
        {
            field: 'placement',
            name: 'Placement',
            width: '10%',
        },
        {
            field: 'start_date',
            name: 'Start date',
            width: '8%',
        },
        {
            field: 'end_date',
            name: 'End date',
            width: '8%',
        },
        {
            field: 'add_to_carts',
            name: 'Add to carts',
            width: '8%',
        },
        {
            field: 'impressions',
            name: 'Impressions',
            width: '10%',
        },
        {
            field: 'gm_per_click',
            name: 'GM per click',
            width: '10%',
        },
        {
            name: 'Actions',
            width: '80px',
            actions: [
                {
                    name: 'View',
                    description: 'View theme',
                    type: 'icon',
                    icon: 'eye',
                    onClick: (theme: any) => navigate(`/curator/themes/${theme.id}`),
                },
                {
                    name: 'Edit',
                    description: 'Edit theme',
                    type: 'icon',
                    icon: 'pencil',
                    onClick: (theme: any) => navigate(`/curator/themes/${theme.id}/edit`),
                },
                {
                    name: 'Delete',
                    description: 'Delete theme',
                    type: 'icon',
                    icon: 'trash',
                    onClick: () => {
                        // Handle delete
                    },
                },
            ],
        },
    ];

    // Filter options for EuiSelectable - multiple selection
    const segmentSelectableOptions: EuiSelectableOption[] = [
        { label: 'Premium', checked: selectedSegments.includes('Premium') ? 'on' : undefined },
        { label: 'Standard', checked: selectedSegments.includes('Standard') ? 'on' : undefined },
        { label: 'Budget', checked: selectedSegments.includes('Budget') ? 'on' : undefined },
    ];

    const statusSelectableOptions: EuiSelectableOption[] = [
        { label: 'Active', checked: selectedStatuses.includes('Active') ? 'on' : undefined },
        { label: 'Ready', checked: selectedStatuses.includes('Ready') ? 'on' : undefined },
        { label: 'Finished', checked: selectedStatuses.includes('Finished') ? 'on' : undefined },
        { label: 'Everlasting', checked: selectedStatuses.includes('Everlasting') ? 'on' : undefined },
        { label: 'Incomplete', checked: selectedStatuses.includes('Incomplete') ? 'on' : undefined },
    ];

    return (
        <ComidaPage breadcrumbs={breadcrumbs} actions={<ActionsBar items={items} />}>
            <EuiPageTemplate restrictWidth={false}>
                <EuiPage>
                    <Sidebar title="Curator" />
                    <EuiPageBody paddingSize="none">
                        <EuiPageTemplate.Section paddingSize="l" grow>
                            {/* Search and Filter Controls */}
                            <EuiFlexGroup gutterSize="m" alignItems="center">
                                <EuiFlexItem grow={2}>
                                    <EuiFieldSearch
                                        placeholder="Find theme"
                                        onChange={handleSearchChange}
                                        isClearable
                                        aria-label="Search themes"
                                    />
                                </EuiFlexItem>
                                <EuiFlexItem grow={1}>
                                    <EuiFilterGroup>
                                        <EuiPopover
                                            button={
                                                <EuiFilterButton
                                                    iconType="arrowDown"
                                                    onClick={() => setIsSegmentPopoverOpen(!isSegmentPopoverOpen)}
                                                    isSelected={isSegmentPopoverOpen}
                                                    hasActiveFilters={selectedSegments.length > 0}
                                                    numActiveFilters={selectedSegments.length}
                                                >
                                                    Segments
                                                </EuiFilterButton>
                                            }
                                            isOpen={isSegmentPopoverOpen}
                                            closePopover={() => setIsSegmentPopoverOpen(false)}
                                            panelPaddingSize="none"
                                        >
                                            <EuiSelectable
                                                searchable
                                                options={segmentSelectableOptions}
                                                onChange={(newOptions) => {
                                                    const selectedLabels = newOptions
                                                        .filter(option => option.checked === 'on')
                                                        .map(option => option.label);
                                                    setSelectedSegments(selectedLabels);
                                                    setPageIndex(0);
                                                }}
                                                listProps={{ bordered: true }}
                                                searchProps={{
                                                    placeholder: 'Search segments',
                                                    compressed: true,
                                                }}
                                            >
                                                {(list, search) => (
                                                    <div style={{ width: 250 }}>
                                                        {search}
                                                        {list}
                                                    </div>
                                                )}
                                            </EuiSelectable>
                                        </EuiPopover>
                                    </EuiFilterGroup>
                                </EuiFlexItem>
                                <EuiFlexItem grow={1}>
                                    <EuiDatePickerRange
                                        startDateControl={
                                            <EuiDatePicker
                                                selected={startDate}
                                                onChange={(date) => {
                                                    setStartDate(date);
                                                    setPageIndex(0);
                                                }}
                                                startDate={startDate}
                                                endDate={endDate}
                                                aria-label="Start date"
                                                placeholder="Start date"
                                                dateFormat="DD/MM/YYYY"
                                            />
                                        }
                                        endDateControl={
                                            <EuiDatePicker
                                                selected={endDate}
                                                onChange={(date) => {
                                                    setEndDate(date);
                                                    setPageIndex(0);
                                                }}
                                                startDate={startDate}
                                                endDate={endDate}
                                                aria-label="End date"
                                                placeholder="End date"
                                                dateFormat="DD/MM/YYYY"
                                            />
                                        }
                                    />
                                </EuiFlexItem>
                                <EuiFlexItem grow={1}>
                                    <EuiFilterGroup>
                                        <EuiPopover
                                            button={
                                                <EuiFilterButton
                                                    iconType="arrowDown"
                                                    onClick={() => setIsStatusPopoverOpen(!isStatusPopoverOpen)}
                                                    isSelected={isStatusPopoverOpen}
                                                    hasActiveFilters={selectedStatuses.length > 0}
                                                    numActiveFilters={selectedStatuses.length}
                                                >
                                                    Status
                                                </EuiFilterButton>
                                            }
                                            isOpen={isStatusPopoverOpen}
                                            closePopover={() => setIsStatusPopoverOpen(false)}
                                            panelPaddingSize="none"
                                        >
                                            <EuiSelectable
                                                searchable
                                                options={statusSelectableOptions}
                                                onChange={(newOptions) => {
                                                    const selectedLabels = newOptions
                                                        .filter(option => option.checked === 'on')
                                                        .map(option => option.label);
                                                    setSelectedStatuses(selectedLabels);
                                                    setPageIndex(0);
                                                }}
                                                listProps={{ bordered: true }}
                                                searchProps={{
                                                    placeholder: 'Search statuses',
                                                    compressed: true,
                                                }}
                                            >
                                                {(list, search) => (
                                                    <div style={{ width: 250 }}>
                                                        {search}
                                                        {list}
                                                    </div>
                                                )}
                                            </EuiSelectable>
                                        </EuiPopover>
                                    </EuiFilterGroup>
                                </EuiFlexItem>
                            </EuiFlexGroup>

                            <EuiSpacer size="l" />

                            <EuiBasicTable
                                items={filteredThemes}
                                columns={columns}
                                pagination={{
                                    pageIndex,
                                    pageSize,
                                    totalItemCount: filteredThemes.length,
                                    pageSizeOptions: [10, 25, 50],
                                }}
                                onChange={onTableChange}
                            />
                        </EuiPageTemplate.Section>
                    </EuiPageBody>
                </EuiPage>
            </EuiPageTemplate>
        </ComidaPage>
    );
};

export default CuratorLandingPage;
