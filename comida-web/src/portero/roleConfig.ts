const commercialRoles = ['oda-commercial-read-only', 'oda-commercial-default'];
const superUserRoles = ['system-super-admin'];

// Exception Roles
export const netFreshRoles = ['netfresh-admin'];

const pricingRoles = ['precio-legacy', ...superUserRoles];
const insightsRoles = ['insights-full-reader', 'insights-reader', ...commercialRoles, ...superUserRoles];
const curatorRoles = ['curator-development'];

const assortmentRoles = [
    'supplier-read-only',
    'supplier-default',
    ...commercialRoles,
    ...superUserRoles,
    ...netFreshRoles,
];

type RoleConfig = {
    requiredRoles: string[];
    href: string;
};

export const roleConfigs: { [key: string]: RoleConfig } = {
    tradeItems: {
        requiredRoles: [...commercialRoles, ...superUserRoles, ...netFreshRoles],
        href: '/products',
    },
    assets: {
        requiredRoles: [...commercialRoles, ...superUserRoles, ...netFreshRoles],
        href: '/assets',
    },
    supplierProducts: {
        requiredRoles: [...commercialRoles, ...superUserRoles],
        href: '/suppliers/products',
    },
    purchasePrices: {
        requiredRoles: assortmentRoles,
        href: '/suppliers/prices',
    },
    pricingScenarios: {
        requiredRoles: pricingRoles,
        href: '/pricing/scenarios',
    },
    priceRecommendations: {
        requiredRoles: pricingRoles,
        href: '/pricing/solver-runs',
    },
    insights: {
        requiredRoles: insightsRoles,
        href: '/insights',
    },
    curator: {
        requiredRoles: curatorRoles,
        href: '/curator',
    },
    kibana: {
        requiredRoles: [...commercialRoles, ...superUserRoles],
        href: '/suppliers/kibana',
    },
    users: {
        requiredRoles: superUserRoles,
        href: '/users',
    },
    help: {
        requiredRoles: [...commercialRoles, ...superUserRoles, ...assortmentRoles, ...pricingRoles, ...insightsRoles],
        href: '/help',
    },
    legalEntities: {
        requiredRoles: [...commercialRoles, ...superUserRoles, ...netFreshRoles],
        href: '/legal-entity',
    },
    definitions: {
        requiredRoles: [...commercialRoles, ...superUserRoles, ...netFreshRoles],
        href: '/definitions',
    },
};
