import { EuiFlexItem, EuiPanel, EuiStat } from '@elastic/eui';

import { MarkupStats } from '@/precio/schemas/scenarios';

interface PriceDistancesProps {
    markupStats: MarkupStats;
    isLoading: boolean;
}

const PriceDistances: React.FC<PriceDistancesProps> = ({ markupStats, isLoading }) => {
    return markupStats[0].price_distances
        .slice()
        .sort((a, b) => {
            const tierOrder: Record<string, number> = {
                supermarket: 1,
                'soft-discounter': 2,
                'hard-discounter': 3,
            };
            const tierDiff =
                (tierOrder[a.pricing_tier as keyof typeof tierOrder] || 99) -
                (tierOrder[b.pricing_tier as keyof typeof tierOrder] || 99);

            if (tierDiff === 0) {
                return a.chain_name.localeCompare(b.chain_name);
            }

            return tierDiff;
        })
        .map((distance) => (
            <EuiFlexItem key={distance.chain_id} grow style={{ maxWidth: '220px' }}>
                <EuiPanel hasShadow={false} hasBorder>
                    <EuiStat
                        titleSize="m"
                        title={`${distance.price_distance}%`}
                        description={`Distance to ${distance.chain_name}`}
                        isLoading={isLoading}
                    >
                        <span style={{ textDecoration: 'line-through', color: '#666' }}>
                            {markupStats[1]?.price_distances.find((d) => d.chain_id === distance.chain_id)
                                ?.price_distance || 'N/A'}
                            %
                        </span>
                    </EuiStat>
                </EuiPanel>
            </EuiFlexItem>
        ));
};

export default PriceDistances;
