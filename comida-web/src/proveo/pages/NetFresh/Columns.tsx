import { EuiFlexGroup } from '@elastic/eui';
import moment from 'moment';

import { BaseCell, DataGridColumn, NumericValueCell, TextValueCell } from '../SupplierProducts/shared/common';
import { ProductImage } from '../SupplierProducts/shared/ProductImage';
import { NetFreshTableRecord } from './api';

export const createColumns = (
    handleRowClick: (item: NetFreshTableRecord) => void,
): Array<DataGridColumn<NetFreshTableRecord>> => [
    {
        id: 'name',
        displayAsText: 'Product name',
        isResizable: true,
        defaultHidden: false,
        render: ({ data }) => (
            <EuiFlexGroup alignItems="center" gutterSize="s">
                <ProductImage gtin={data.consumer_unit_gtin} width={32} height={32} />
                <TextValueCell data={data} value={data.name} onClick={handleRowClick} />
            </EuiFlexGroup>
        ),
    },
    {
        id: 'supplier_product_id',
        displayAsText: 'Supplier Product ID',
        isResizable: true,
        schema: 'string',
        defaultHidden: false,
        render: ({ data }) => (
            <TextValueCell data={data} value={data.supplier_product_id?.toString()} onClick={handleRowClick} />
        ),
    },
    {
        id: 'pricing_unit',
        displayAsText: 'Pricing Unit',
        isResizable: true,
        schema: 'string',
        defaultHidden: false,
        render: ({ data }) => <TextValueCell data={data} value={data.pricing_unit} onClick={handleRowClick} />,
    },
    {
        id: 'valid_from_date',
        displayAsText: 'Valid from',
        isResizable: true,
        defaultHidden: false,
        render: ({ data }: { data: NetFreshTableRecord }) => {
            const formattedDate = moment(data.valid_from_date).format('DD MMM YYYY');
            return (
                <BaseCell data={data} onClick={handleRowClick}>
                    {formattedDate}
                </BaseCell>
            );
        },
    },
    {
        id: 'list_price',
        displayAsText: 'NetFresh List Price',
        isResizable: true,
        schema: 'numeric',
        defaultHidden: false,
        render: ({ data }: { data: NetFreshTableRecord }) => {
            return <NumericValueCell data={data} value={data.list_price} onClick={handleRowClick} />;
        },
    },
    {
        id: 'oda_list_price',
        displayAsText: 'Oda List Price',
        isResizable: true,
        schema: 'numeric',
        defaultHidden: false,
        render: ({ data }: { data: NetFreshTableRecord }) => {
            return <NumericValueCell data={data} value={data.oda_list_price} onClick={handleRowClick} />;
        },
    },
    {
        id: 'oda_joint_marketing',
        displayAsText: 'Oda Joint Marketing',
        isResizable: true,
        schema: 'numeric',
        defaultHidden: false,
        render: ({ data }: { data: NetFreshTableRecord }) => {
            return <NumericValueCell data={data} value={data.oda_joint_marketing} onClick={handleRowClick} />;
        },
    },
    {
        id: 'bonus',
        displayAsText: 'Bonus',
        isResizable: true,
        schema: 'numeric',
        defaultHidden: false,
        render: ({ data }: { data: NetFreshTableRecord }) => {
            return <NumericValueCell data={data} value={data.bonus} onClick={handleRowClick} />;
        },
    },
    {
        id: 'markup',
        displayAsText: 'Markup',
        isResizable: true,
        schema: 'numeric',
        defaultHidden: false,
        render: ({ data }: { data: NetFreshTableRecord }) => {
            return <NumericValueCell data={data} value={data.markup} onClick={handleRowClick} />;
        },
    },
];
