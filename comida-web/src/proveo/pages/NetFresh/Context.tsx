import { Eui<PERSON>allOut, EuiPageTemplate, EuiSearchBar, Query } from '@elastic/eui';
import { createContext, useContext, useEffect, useMemo, useState } from 'react';

import { NON_DESCENDANT_CONTEXT } from '@/proveo/common';
import { useSupplierContext } from '@/proveo/shared/SupplierComponents/SupplierContext';

import { NetFreshTableRecord } from './api';
import { useNetFreshPrices } from './netFreshQueries';

interface NetFreshContextProps {
    prices: NetFreshTableRecord[];
    setSearchQuery: (query: Query) => void;
}

export const NetFreshContext = createContext<NetFreshContextProps | typeof NON_DESCENDANT_CONTEXT>(
    NON_DESCENDANT_CONTEXT,
);

export function useNetFreshContext() {
    const context = useContext(NetFreshContext);

    if (context === NON_DESCENDANT_CONTEXT) {
        throw new Error('useNetFreshContext must be used within a NetFreshProvider');
    }

    return useMemo(() => context, [context]);
}

const useNetFreshData = () => {
    const [isWorking, setWorking] = useState(false);
    const { supplierId } = useSupplierContext();

    const { data, isLoading, isError } = useNetFreshPrices({ supplierId });

    return {
        data,
        isLoading,
        isError,
        isWorking,
        setWorking,
    };
};

interface NetFreshProviderProps {
    children: React.ReactNode;
    loadingFallback?: React.ReactNode;
}

export const NetFreshProvider: React.FC<NetFreshProviderProps> = ({ children, loadingFallback }) => {
    const { data, isLoading, isError } = useNetFreshData();
    const [tableData, setTableData] = useState<NetFreshTableRecord[]>(data ?? []);

    const [searchQuery, setSearchQuery] = useState<Query>(EuiSearchBar.Query.MATCH_ALL);

    useEffect(() => {
        setTableData(EuiSearchBar.Query.execute(searchQuery, data ?? []));
    }, [data, searchQuery]);

    if (isError) {
        return (
            <EuiPageTemplate>
                <EuiCallOut title="Error" color="danger" iconType="alert">
                    Unable to load content.
                </EuiCallOut>
            </EuiPageTemplate>
        );
    }

    if (isLoading || !data) {
        return loadingFallback || <div>Loading...</div>;
    }

    return (
        <NetFreshContext.Provider value={{ prices: tableData, setSearchQuery }}>{children}</NetFreshContext.Provider>
    );
};
