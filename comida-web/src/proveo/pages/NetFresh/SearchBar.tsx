import { EuiFlexGroup, EuiFlexItem, EuiSearchBar, EuiSearchBarOnChangeArgs } from '@elastic/eui';

import { useDebounceFn } from '@/proveo/common';

import { useNetFreshContext } from './Context';

export const SearchBar = () => {
    const { setSearchQuery } = useNetFreshContext();

    function handleQueryChange({ query: newQuery, error }: EuiSearchBarOnChangeArgs) {
        if (error) {
            console.log('Invalid search');
        } else {
            setSearchQuery(newQuery);
        }
    }

    return (
        <EuiFlexGroup gutterSize="m">
            <EuiFlexItem>
                <EuiSearchBar
                    box={{ placeholder: 'Search', incremental: true }}
                    onChange={useDebounceFn(handleQueryChange, 500)}
                />
            </EuiFlexItem>
        </EuiFlexGroup>
    );
};
