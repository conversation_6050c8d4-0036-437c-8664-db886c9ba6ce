import { PricingUnit } from '../PurchasePrices/api';

export interface NetfreshExcelImport {
    file: File;
    valid_from_date: Date;
    markup: number;
    supplierId: number;
}

export interface NetFreshPriceRecord {
    supplier_product_id: number;
    valid_from_date: Date;
    pricing_unit: string;
    list_price: number;
    bonus: number | null;
    markup: number;
}

export interface NetFreshTableRecord {
    name: string;
    supplier_product_id: number;
    consumer_unit_gtin: string;
    valid_from_date: Date;
    pricing_unit: PricingUnit;
    list_price: number;
    bonus: number | null;
    markup: number;
    oda_list_price: number;
    oda_joint_marketing: number | null;
}
