import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>uttonEmpty,
    <PERSON>uiDate<PERSON>icker,
    EuiFieldText,
    EuiForm,
    EuiFormRow,
    EuiPopover,
    EuiSpacer,
} from '@elastic/eui';
import { useQueryClient } from '@tanstack/react-query';
import moment from 'moment';
import { useRef, useState } from 'react';

import { useToasts } from '@/components/eui/ToastNotification';
import { useExcelImportStatus } from '@/proveo/shared/ExcelImportStatus/ExcelImportStatusProvider';
import { useSupplierContext } from '@/proveo/shared/SupplierComponents/SupplierContext';

import { useConvertNetFreshTemplate } from './netFreshQueries';

export const ImportPricesAction = () => {
    const queryClient = useQueryClient();
    const { supplierId } = useSupplierContext();
    const { notifyError } = useExcelImportStatus();
    const [isWorking, setWorking] = useState(false);
    const [isPopoverOpen, setIsPopoverOpen] = useState(false);
    const [validFromDate, setValidFromDate] = useState(moment());
    const [markup, setMarkup] = useState('0.15');
    const convertNetFreshTemplate = useConvertNetFreshTemplate(notifyError);
    const { addSuccessToast, addErrorToast, addInformationToast } = useToasts();
    const fileInputRef = useRef(null);

    const onButtonClick = () => {
        if (!supplierId) {
            addInformationToast('Select a supplier before importing.');
            return;
        }
        setIsPopoverOpen(true);
    };

    const closePopover = () => {
        setIsPopoverOpen(false);
    };

    const handleImportClick = () => {
        if (fileInputRef.current) {
            (fileInputRef.current as HTMLInputElement).click();
        }
        closePopover();
    };

    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        if (!supplierId) {
            return;
        }
        const input = event.target;
        setWorking(true);
        if (input.files) {
            const file = input.files[0];
            convertNetFreshTemplate.mutate(
                {
                    file,
                    supplierId,
                    valid_from_date: validFromDate.toDate(),
                    markup: parseFloat(markup),
                },
                {
                    onSuccess: () => {
                        queryClient.invalidateQueries(['NetFreshPrices']);
                        addSuccessToast('Excel file uploaded successfully.');
                        setWorking(false);
                    },
                    onError: (error: any) => {
                        queryClient.invalidateQueries(['NetFreshPrices']);
                        addErrorToast(error.message || 'Something went wrong while uploading the excel file');
                        setWorking(false);
                    },
                },
            );
            if (input && input.files.length > 0) {
                input.value = '';
            }
        }
    };

    const handleMarkupChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setMarkup(e.target.value);
    };

    const handleDateChange = (date: moment.Moment) => {
        setValidFromDate(date);
    };

    const button = (
        <>
            {isWorking ? (
                <EuiButton size="s" fill isLoading>
                    Working&hellip;
                </EuiButton>
            ) : (
                <EuiButtonEmpty iconType="download" size="s" onClick={onButtonClick}>
                    Import products
                </EuiButtonEmpty>
            )}
        </>
    );

    return (
        <>
            <input
                type="file"
                accept=".xlsx"
                onChange={handleFileUpload}
                style={{ display: 'none' }}
                ref={fileInputRef}
            />

            <EuiPopover
                button={button}
                isOpen={isPopoverOpen}
                closePopover={closePopover}
                panelPaddingSize="m"
                anchorPosition="downLeft"
            >
                <div style={{ width: '300px' }}>
                    <EuiForm>
                        <EuiFormRow label="Valid from">
                            <EuiDatePicker
                                selected={validFromDate}
                                onChange={handleDateChange}
                                dateFormat="DD MMM YYYY"
                                fullWidth
                            />
                        </EuiFormRow>

                        <EuiFormRow label="Markup (%)">
                            <EuiFieldText value={markup} onChange={handleMarkupChange} fullWidth />
                        </EuiFormRow>

                        <EuiSpacer size="m" />

                        <EuiButton fill size="s" onClick={handleImportClick} fullWidth>
                            Import
                        </EuiButton>
                    </EuiForm>
                </div>
            </EuiPopover>
        </>
    );
};
