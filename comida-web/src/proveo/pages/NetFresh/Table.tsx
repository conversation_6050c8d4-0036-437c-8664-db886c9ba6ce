import { EuiDataGrid, EuiFlexGroup } from '@elastic/eui';
import { css } from '@emotion/react';
import { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { useSupplierContext } from '@/proveo/shared/SupplierComponents/SupplierContext';

import { NetFreshTableRecord } from './api';
import { createColumns } from './Columns';
import { useNetFreshContext } from './Context';
import Flyout from './Prices';
import { SearchBar } from './SearchBar';

export const NetFreshPricesTable = () => {
    const navigate = useNavigate();
    const { productId } = useParams();
    const { supplierId } = useSupplierContext();
    const { prices } = useNetFreshContext();

    const [selectedRow, setSelectedRow] = useState<NetFreshTableRecord | undefined>(undefined);

    useEffect(() => {
        if (productId) {
            const selectedProduct = prices.find((product) => product.supplier_product_id === Number(productId));
            if (selectedProduct) {
                setSelectedRow(selectedProduct);
            }
        }
    }, [productId, prices]);

    function clearEditingRow() {
        setSelectedRow(undefined);
        navigate(`/suppliers/${supplierId}/netfresh/prices/`);
    }

    const handleRowClick = (item: NetFreshTableRecord) => {
        setSelectedRow(item);
        navigate(`/suppliers/${supplierId}/netfresh/prices/${item.supplier_product_id}/`);
    };
    const columns = createColumns(handleRowClick);
    const [visibleColumns, setVisibleColumns] = useState(columns.map((column) => column.id));

    const renderCellValue = ({ rowIndex, columnId }: { rowIndex: number; columnId: string }) => {
        const product = prices[rowIndex];
        if (!product) return null;
        const column = columns.find((col) => col.id === columnId);
        if (!column) return null;
        return column.render({
            data: product,
            setCellProps: () => ({}),
        });
    };

    const onColumnVisibilityChange = (newVisibleColumns: any) => {
        setVisibleColumns(newVisibleColumns);
    };

    const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 25 });
    const onChangeItemsPerPage = useCallback(
        (pageSize: number) =>
            setPagination((currentPagination) => ({
                ...currentPagination,
                pageSize,
                pageIndex: 0,
            })),
        [setPagination],
    );
    const onChangePage = useCallback(
        (pageIndex: number) => setPagination((currentPagination) => ({ ...currentPagination, pageIndex })),
        [setPagination],
    );

    return (
        <EuiFlexGroup
            css={css`
                height: calc(100vh - 150px);
            `}
            direction="column"
            gutterSize="s"
        >
            <SearchBar />
            <EuiDataGrid
                aria-label="NetFresh grid"
                columns={columns}
                columnVisibility={{
                    visibleColumns,
                    setVisibleColumns: onColumnVisibilityChange,
                }}
                rowCount={prices.length}
                renderCellValue={renderCellValue}
                toolbarVisibility={{
                    additionalControls: {
                        right: (
                            <EuiFlexGroup>
                                <span style={{ marginRight: '20px' }}>{prices.length ?? 0} products</span>
                            </EuiFlexGroup>
                        ),
                    },
                }}
                pagination={{
                    ...pagination,
                    pageSizeOptions: [25, 50],
                    onChangeItemsPerPage,
                    onChangePage,
                }}
            />
            <Flyout row={selectedRow} onClose={clearEditingRow} />
        </EuiFlexGroup>
    );
};
