import { EuiPageTemplate } from '@elastic/eui';

import BaseProveoPage from './BaseProveoPage';

export const ProveoUnauthorizedPage: React.FC = () => {
    return (
        <>
            <BaseProveoPage panelled breadcrumbs={[]}>
                <EuiPageTemplate.EmptyPrompt
                    iconType="warning"
                    color="danger"
                    title={<h2>Your user is not authorized.</h2>}
                    body={
                        <p>
                            You do not have the necessary permissions to view this page. If this seems incorrect, please
                            get in touch with support.
                        </p>
                    }
                />
            </BaseProveoPage>
        </>
    );
};
