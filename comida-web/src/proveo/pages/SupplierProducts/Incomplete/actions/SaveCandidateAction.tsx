import { EuiButton, EuiFlexGroup, EuiFlexItem } from '@elastic/eui';
import { useQueryClient } from '@tanstack/react-query';

import { useToasts } from '@/components/eui/ToastNotification';
import { useUpdateSupplierProductCandidate } from '@/proveo/pages/SupplierProducts/supplierProductsQueries';

import { useCandidateFlyoutContext } from '../Flyout/Context';

type SaveCandidateActionProps = {
    closeFlyout: () => void;
};

export const SaveCandidateAction = ({ closeFlyout }: SaveCandidateActionProps) => {
    const queryClient = useQueryClient();
    const updateCandidatetMutation = useUpdateSupplierProductCandidate();

    const { candidate } = useCandidateFlyoutContext();
    const { addSuccessToast, addErrorToast } = useToasts();

    const handleUpdate = () => {
        updateCandidatetMutation.mutate(candidate, {
            onSuccess: () => {
                queryClient.invalidateQueries(['SupplierProductCandidates']);
                addSuccessToast('Supplier product updated successfully.');
                closeFlyout();
            },
            onError: (error: any) => {
                addErrorToast(error.message || 'Failed to update supplier product.');
            },
        });
    };

    return (
        <EuiFlexGroup alignItems="baseline" justifyContent="flexEnd">
            <EuiFlexItem grow={false}>
                <EuiButton fill size="s" onClick={handleUpdate}>
                    Save
                </EuiButton>
            </EuiFlexItem>
        </EuiFlexGroup>
    );
};
