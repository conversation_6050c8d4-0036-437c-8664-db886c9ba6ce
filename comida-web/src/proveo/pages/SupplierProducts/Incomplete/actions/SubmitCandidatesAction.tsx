import { EuiButton } from '@elastic/eui';
import { useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';

import { useToasts } from '@/components/eui/ToastNotification';
import { useSupplierContext } from '@/proveo/shared/SupplierComponents/SupplierContext';

import { useSubmitCandidates } from '../../supplierProductsQueries';

interface SubmitCandidatesActionProps {
    selectedCandidateIds: number[];
    onActionComplete: () => void;
}

export const SubmitCandidatesAction: ReactFC<SubmitCandidatesActionProps> = ({
    selectedCandidateIds,
    onActionComplete,
}) => {
    const queryClient = useQueryClient();
    const { addErrorToast, addSuccessToast, addInformationToast } = useToasts();
    const { supplierId } = useSupplierContext();
    const SubmitCandidatesMutation = useSubmitCandidates(supplierId);
    const [isWorking, setIsWorking] = useState<boolean>(false);

    const handleSubmit = async () => {
        if (selectedCandidateIds.length === 0) {
            addInformationToast('There are no candidates selected to submit');
            return;
        }
        setIsWorking(true);
        try {
            await SubmitCandidatesMutation.mutateAsync({
                candidate_ids: selectedCandidateIds,
            });
            addSuccessToast('Candidates submitted successfully');
            queryClient.invalidateQueries(['SupplierProductCandidates']);
            onActionComplete();
        } catch (error: any) {
            queryClient.invalidateQueries(['SupplierProductCandidates']);
            onActionComplete();
            addErrorToast(error?.message || 'Failed to submit candidates');
        } finally {
            setIsWorking(false);
        }
    };

    return (
        <>
            {isWorking ? (
                <EuiButton size="s" isLoading fill data-test-subj="Submit-candidates-action-loading">
                    Working&hellip;
                </EuiButton>
            ) : (
                <EuiButton
                    size="s"
                    iconType="push"
                    color="primary"
                    onClick={handleSubmit}
                    data-test-subj="Submit-candidates-action"
                >
                    Submit{selectedCandidateIds.length > 0 ? ` (${selectedCandidateIds.length})` : ''}
                </EuiButton>
            )}
        </>
    );
};
