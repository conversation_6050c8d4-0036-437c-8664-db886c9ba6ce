import { EuiButton } from '@elastic/eui';
import { useQueryClient } from '@tanstack/react-query';

import { useToasts } from '@/components/eui/ToastNotification';
import { useSupplierContext } from '@/proveo/shared/SupplierComponents/SupplierContext';

import { useDeleteCandidates } from '../../supplierProductsQueries';

interface DeleteCandidatesActionProps {
    selectedCandidateIds: number[];
    onActionComplete: () => void;
}

export const DeleteCandidatesAction: ReactFC<DeleteCandidatesActionProps> = ({
    selectedCandidateIds,
    onActionComplete,
}) => {
    const queryClient = useQueryClient();
    const { addErrorToast, addSuccessToast } = useToasts();
    const { supplierId } = useSupplierContext();
    const deleteCandidatesMutation = useDeleteCandidates(supplierId);

    const handleDelete = async () => {
        try {
            await deleteCandidatesMutation.mutateAsync({
                candidate_ids: selectedCandidateIds,
            });
            addSuccessToast('Candidates deleted successfully');
            queryClient.invalidateQueries(['SupplierProductCandidates']);
            onActionComplete();
        } catch (error: any) {
            queryClient.invalidateQueries(['SupplierProductCandidates']);
            onActionComplete();
            addErrorToast(error?.message || 'Failed to delete candidates');
        }
    };

    return (
        <EuiButton
            size="s"
            iconType="trash"
            color="danger"
            onClick={handleDelete}
            data-test-subj="delete-candidates-action"
        >
            Delete
        </EuiButton>
    );
};
