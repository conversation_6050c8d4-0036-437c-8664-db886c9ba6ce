import { Eui<PERSON><PERSON>on, EuiButtonEmpty } from '@elastic/eui';
import { useQueryClient } from '@tanstack/react-query';
import { useRef, useState } from 'react';

import { useToasts } from '@/components/eui/ToastNotification';
import { useSupplierProductsUploadExcel } from '@/proveo/pages/SupplierProducts/supplierProductsQueries';
import { useExcelImportStatus } from '@/proveo/shared/ExcelImportStatus/ExcelImportStatusProvider';
import { useSupplierContext } from '@/proveo/shared/SupplierComponents/SupplierContext';

export const ImportCandidatesAction: ReactFC = () => {
    const queryClient = useQueryClient();
    const { supplierId } = useSupplierContext();
    const { notifyError } = useExcelImportStatus();
    const [isWorking, setWorking] = useState(false);
    const uploadExcel = useSupplierProductsUploadExcel(notifyError);

    const { addSuccessToast, addErrorToast, addInformationToast } = useToasts();

    const handleButtonClick = () => {
        if (!supplierId) {
            addInformationToast('Select a supplier before importing.');
            return;
        }
        if (fileInputRef.current) {
            (fileInputRef.current as HTMLInputElement).click();
        }
    };

    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const input = event.target;
        setWorking(true);
        if (input.files) {
            const file = input.files[0];
            uploadExcel.mutate(
                { file, supplierId },
                {
                    onSuccess: () => {
                        queryClient.invalidateQueries(['SupplierProductCandidates']);
                        addSuccessToast('Excel file uploaded successfully.');
                        setWorking(false);
                    },
                    onError: (error: any) => {
                        queryClient.invalidateQueries(['SupplierProductCandidates']);
                        addErrorToast(error.message || 'Something went wrong while uploading the excel file');
                        setWorking(false);
                    },
                },
            );
            if (input && input.files.length > 0) {
                input.value = '';
            }
        }
    };

    const fileInputRef = useRef(null);

    return (
        <>
            <input
                type="file"
                accept=".xlsx"
                onChange={handleFileUpload}
                style={{ display: 'none' }}
                ref={fileInputRef}
            />
            {isWorking ? (
                <EuiButton size="s" fill isLoading>
                    Working&hellip;
                </EuiButton>
            ) : (
                <EuiButtonEmpty iconType="download" size="s" onClick={handleButtonClick}>
                    Import products
                </EuiButtonEmpty>
            )}
        </>
    );
};
