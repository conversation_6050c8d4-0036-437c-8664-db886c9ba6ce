import { EuiFlyout } from '@elastic/eui';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { CandidateFlyoutProvider } from './Context';
import { CandidateFlyoutTabs } from './Tabs';

export default function CandidateFlyout() {
    const [isFlyoutVisible, setFlyoutVisible] = useState<boolean>(false);
    const navigate = useNavigate();
    const { supplierId, candidateId } = useParams();
    useEffect(() => {
        if (candidateId) {
            setFlyoutVisible(true);
        }
    }, [candidateId]);

    const closeFlyout = () => {
        setFlyoutVisible(false);
        navigate(`/suppliers/${supplierId}/products/draft/`);
    };

    if (!isFlyoutVisible || !candidateId) {
        return null;
    }

    return (
        <CandidateFlyoutProvider candidateId={Number(candidateId)}>
            <EuiFlyout onClose={closeFlyout} size="l" maxWidth={800}>
                <CandidateFlyoutTabs closeFlyout={closeFlyout} />
            </EuiFlyout>
        </CandidateFlyoutProvider>
    );
}
