import { EuiCallOut, EuiPageTemplate } from '@elastic/eui';
import { createContext, useContext, useEffect, useMemo, useState } from 'react';

import { NON_DESCENDANT_CONTEXT } from '@/proveo/common';
import { Term } from '@/proveo/pages/PurchasePrices/api';
import { useSupplierContext } from '@/proveo/shared/SupplierComponents/SupplierContext';

import { SupplierProductCandidateFlyoutRecord } from '../../api';
import { useSupplierProductCandidate } from '../../supplierProductsQueries';

interface CandidateFlyoutContextProps {
    candidate: SupplierProductCandidateFlyoutRecord;
    handleInputChange: (e: React.ChangeEvent<HTMLElement>) => void;
    handleTermValuesChange: (values: Partial<Term>) => void;
}

export const CandidateFlyoutContext = createContext<CandidateFlyoutContextProps | typeof NON_DESCENDANT_CONTEXT>(
    NON_DESCENDANT_CONTEXT,
);

export function useCandidateFlyoutContext() {
    const context = useContext(CandidateFlyoutContext);

    if (context === NON_DESCENDANT_CONTEXT) {
        throw new Error('useCandidateFlyoutContext must be used within a CandidateFlyoutProvider');
    }

    return useMemo(() => context, [context]);
}

interface CandidateFlyoutProviderProps {
    children: React.ReactNode;
    candidateId: number;
}

const useCandidateData = ({ supplierId, candidateId }: { supplierId?: number; candidateId: number }) => {
    const { data, isLoading, isError } = useSupplierProductCandidate({ supplierId, candidateId });

    return {
        data,
        isLoading,
        isError,
    };
};

export const CandidateFlyoutProvider: React.FC<CandidateFlyoutProviderProps> = ({ children, candidateId }) => {
    const { supplierId } = useSupplierContext();
    const { data, isLoading, isError } = useCandidateData({ supplierId, candidateId });
    const [candidate, setCandidate] = useState<SupplierProductCandidateFlyoutRecord>(
        {} as SupplierProductCandidateFlyoutRecord,
    );

    useEffect(() => {
        if (data) {
            setCandidate(data);
        }
    }, [data]);

    if (isError) {
        return (
            <EuiPageTemplate>
                <EuiCallOut title="Error" color="danger" iconType="alert">
                    Unable to load content.
                </EuiCallOut>
            </EuiPageTemplate>
        );
    }

    if (isLoading || !candidate) {
        return <div>Loading...</div>;
    }

    function handleTermValuesChange(values: Partial<Term>) {
        setCandidate((prev) => ({
            ...prev,
            term: {
                ...prev.term!,
                ...values,
            },
        }));
    }

    const handleInputChange = (e: React.ChangeEvent<any>) => {
        const { name, value } = e.target;
        setCandidate((prev) => ({
            ...prev,
            // if the field exists on term, update nested term
            ...(prev.term && name in prev.term ? { term: { ...prev.term, [name]: value } } : { [name]: value }),
        }));
    };

    return (
        <CandidateFlyoutContext.Provider value={{ candidate, handleInputChange, handleTermValuesChange }}>
            {children}
        </CandidateFlyoutContext.Provider>
    );
};
