import { EuiFlexItem } from '@elastic/eui';

import { ProductFormRow, ProductFormSection, TextInput } from '@/proveo/components/flyout/FormComponents';
import { useProveoUser } from '@/proveo/hooks';
import { SupplierComboBox } from '@/proveo/shared/SupplierComponents/SupplierComboBox';

import { GtinSelector } from '../../shared/GtinSelector';
import { useCandidateFlyoutContext } from './Context';

export const CandidateInformation = () => {
    const { country } = useProveoUser();
    const { candidate, handleInputChange } = useCandidateFlyoutContext();

    return (
        <>
            <ProductFormSection title="General">
                <EuiFlexItem>
                    <ProductFormRow label="Supplier">
                        <SupplierComboBox isDisabled />
                    </ProductFormRow>
                </EuiFlexItem>
                <TextInput label="Product Name" name="name" value={candidate.name ?? ''} onChange={handleInputChange} />
                <TextInput
                    label="Supplier internal product ID"
                    name="internal_product_number"
                    value={candidate.internal_product_number ?? ''}
                    onChange={handleInputChange}
                />
                <TextInput
                    label="Product Number (EPD)"
                    name="product_number"
                    value={candidate.product_number ?? ''}
                    onChange={handleInputChange}
                />
                <TextInput
                    label="Consumer Unit GTIN"
                    name="consumer_unit_gtin"
                    value={candidate.consumer_unit_gtin ?? ''}
                    onChange={handleInputChange}
                />
                <GtinSelector
                    label="Purchasing Unit GTIN"
                    name="purchasing_unit_gtin"
                    targetMarket={country ?? 'NO'}
                    hierarchyGtin={candidate.consumer_unit_gtin ?? ''}
                    selectedGtin={candidate.purchasing_unit_gtin ?? ''}
                    productNumber={candidate.product_number ?? ''}
                    handleChange={handleInputChange}
                />
            </ProductFormSection>
        </>
    );
};
