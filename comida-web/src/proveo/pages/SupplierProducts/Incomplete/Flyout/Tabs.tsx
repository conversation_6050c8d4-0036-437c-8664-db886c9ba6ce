import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>, EuiTab<PERSON> } from '@elastic/eui';
import { css } from '@emotion/react';
import { useState } from 'react';

import { ProductFlyoutHeader } from '@/proveo/components/flyout/ProductFlyoutHeader';

import { CandidateStatusBadge } from '../../shared/common';
import { SaveCandidateAction } from '../actions/SaveCandidateAction';
import { useCandidateFlyoutContext } from './Context';
import { CandidateInformation } from './Information';
import { CandidatePrices } from './Prices';

type CandidateFlyoutTabsProps = {
    closeFlyout: () => void;
};

export const CandidateFlyoutTabs = ({ closeFlyout }: CandidateFlyoutTabsProps) => {
    const { candidate } = useCandidateFlyoutContext();
    const [selectedTabId, setSelectedTabId] = useState<string>('information');

    const tabs = [
        {
            id: 'information',
            name: 'Information',
            disabled: false,
        },
        {
            id: 'prices',
            name: 'Prices',
            disabled: false,
        },
    ];

    const onTabClick = (id: string) => {
        setSelectedTabId(id);
    };

    return (
        <>
            <EuiFlyoutHeader hasBorder>
                <ProductFlyoutHeader
                    gtin={candidate.consumer_unit_gtin ?? ''}
                    name={candidate.name ?? ''}
                    statusBadge={<CandidateStatusBadge data={candidate} />}
                />
                <EuiTabs
                    css={css`
                        margin-bottom: -25px;
                    `}
                >
                    {tabs.map((tab) => (
                        <EuiTab
                            key={tab.id}
                            onClick={() => onTabClick(tab.id)}
                            isSelected={tab.id === selectedTabId}
                            disabled={tab.disabled}
                        >
                            {tab.name}
                        </EuiTab>
                    ))}
                </EuiTabs>
            </EuiFlyoutHeader>

            <EuiFlyoutBody>
                {selectedTabId === 'information' ? <CandidateInformation /> : <CandidatePrices />}
            </EuiFlyoutBody>
            <EuiFlyoutFooter>
                <SaveCandidateAction closeFlyout={closeFlyout} />
            </EuiFlyoutFooter>
        </>
    );
};
