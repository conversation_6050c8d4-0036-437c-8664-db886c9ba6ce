import { EuiSpacer } from '@elastic/eui';

import { DiscountsSection } from '@/proveo/components/flyout/discounts';
import { TermsSection } from '@/proveo/components/flyout/Terms';
import { decorateTermWithCalcFunctions } from '@/proveo/shared/termDecorator';

import { useCandidateFlyoutContext } from './Context';

export const CandidatePrices = () => {
    const { candidate, handleInputChange, handleTermValuesChange } = useCandidateFlyoutContext();
    if (!candidate || !candidate.term) {
        return null;
    }
    const { term } = candidate;
    // Hardcoded currency code for Norway,need to be replaced with a dynamic value
    // when we add variable weight and sweden
    const currencyCode = 'NOK';
    return (
        <>
            <TermsSection
                term={decorateTermWithCalcFunctions(term)}
                hasVariableWeight={false}
                currencyCode={currencyCode}
                handleEventChange={handleInputChange}
                handleTermValuesChange={handleTermValuesChange}
            />
            <EuiSpacer size="s" />
            <DiscountsSection
                currencyCode={currencyCode}
                term={decorateTermWithCalcFunctions(term)}
                handleEventChange={handleInputChange}
                handleTermValuesChange={handleTermValuesChange}
            />
        </>
    );
};
