import { EuiComboBox, EuiFormRow } from '@elastic/eui';
import { ChangeEvent, useEffect, useState } from 'react';

import { TradeItemHierarchyRecord } from '@/piscina/api';
import { useHierarchy } from '@/proveo/pages/SupplierProducts/supplierProductsQueries';

interface GtinSelectorProps {
    targetMarket: string;
    hierarchyGtin: string;
    selectedGtin: string;
    label: string;
    name: string;
    productNumber: string;
    handleChange: (event: ChangeEvent<HTMLSelectElement>) => void;
    setError?: any;
}

function extractGtins(records: TradeItemHierarchyRecord[], product_number: string): string[] {
    const gtins: Set<string> = new Set();

    function recurse(record: TradeItemHierarchyRecord) {
        const hasEpdIdentifier = record.identifier_module?.identifiers?.some((id) => id.identifier_type === 'EPD');

        if (
            record.gtin &&
            (!hasEpdIdentifier || // If no EPD identifier exists, add the GTIN
                record.identifier_module?.identifiers?.some(
                    (id) => id.identifier_type === 'EPD' && id.identifier_value === product_number, // Match with product_number if EPD exists
                ))
        ) {
            gtins.add(record.gtin);
        }
        if (record.children && record.children.length > 0) {
            record.children.forEach((child) => recurse(child));
        }
    }

    records.forEach((record) => recurse(record));

    return Array.from(gtins);
}

export const GtinSelector = ({
    targetMarket,
    hierarchyGtin,
    selectedGtin,
    label,
    name,
    productNumber,
    handleChange,
    setError,
}: GtinSelectorProps) => {
    const { data, isError } = useHierarchy({ targetMarket, gtin: hierarchyGtin });

    const [selectedOptions, setSelectedOptions] = useState<Array<{ label: string }>>([]);

    useEffect(() => {
        if (setError) {
            if (isError) {
                setError('consumer_unit_gtin', 'GTIN could not be found.');
            } else {
                setError('consumer_unit_gtin', '');
            }
        }
    }, [isError, setError]);

    useEffect(() => {
        if (selectedGtin) {
            setSelectedOptions([{ label: selectedGtin }]);
        } else {
            setSelectedOptions([]);
        }
    }, [selectedGtin]);

    const gtins = extractGtins(data ?? [], productNumber);

    const options = gtins.map((gtin) => ({ label: gtin }));

    const onChange = (newSelectedOptions: Array<{ label: string }>) => {
        const selectedValue = newSelectedOptions.length > 0 ? newSelectedOptions[0].label : '';

        const syntheticEvent = {
            target: {
                name,
                value: selectedValue,
            },
        } as ChangeEvent<HTMLSelectElement>;

        handleChange(syntheticEvent);
    };

    return (
        <EuiFormRow
            fullWidth
            display="columnCompressed"
            label={label}
            isInvalid={isError}
            error={isError && 'Resolve Consumer GTIN error to load Purchasing unit GTINs.'}
        >
            <EuiComboBox
                placeholder=""
                singleSelection={{ asPlainText: true }}
                options={options}
                selectedOptions={selectedOptions}
                onChange={onChange}
                isClearable
                data-test-subj={name}
            />
        </EuiFormRow>
    );
};
