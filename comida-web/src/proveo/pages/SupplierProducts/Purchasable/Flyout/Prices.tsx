import { ComputedPricesTable } from '@/proveo/components/flyout/ComputedPricesTable';
import {
    ProductFormSection,
    ProductFormSubsection,
    ReadOnlyTextInput,
} from '@/proveo/components/flyout/FormComponents';

import { useFlyoutContext } from './FlyoutContext';

export const Prices = () => {
    const { supplierProduct, term, computedPrices } = useFlyoutContext();
    const consumerUnitPrice = computedPrices.find((price) => price.gtin === supplierProduct.consumer_unit_gtin);
    const purchasingUnitPrice = computedPrices.find((price) => price.gtin === supplierProduct.purchasing_unit_gtin);

    if (!consumerUnitPrice || !purchasingUnitPrice) {
        return null;
    }

    return (
        <>
            <ProductFormSection title="Terms">
                <ReadOnlyTextInput label="Pricing Unit" value={term?.pricing_unit ?? 'FPK'} />
                <ReadOnlyTextInput label="Price valid from" value={term?.valid_from_date ?? 'N/A'} />
                <ReadOnlyTextInput label="List price per pricing unit" value={term?.list_price?.toString() ?? ''} />
                <ReadOnlyTextInput label="Shipping cost" value={term?.supplier_shipping_cost?.toString() ?? ''} />
            </ProductFormSection>
            <ProductFormSection title="Discounts">
                <ReadOnlyTextInput
                    label="Supplier discount (%)"
                    value={term?.supplier_discount_percentage?.toString() ?? ''}
                />
                <ReadOnlyTextInput label="Supplier discount (NOK)" value={term?.supplier_discount?.toString() ?? ''} />
                <ReadOnlyTextInput
                    label="Other discount (%)"
                    value={term?.other_discount_percentage?.toString() ?? ''}
                />
                <ReadOnlyTextInput label="Other discount (NOK)" value={term?.other_discount?.toString() ?? ''} />
                <ReadOnlyTextInput label="Discount tax" value={term?.discount_tax_amount?.toString() ?? ''} />
                <ProductFormSubsection title="Joint marketing">
                    <ReadOnlyTextInput
                        label="Joint marketing calculation"
                        value={term?.joint_marketing_terms?.toString() ?? ''}
                    />
                    <ReadOnlyTextInput
                        label="Joint marketing (%)"
                        value={term?.joint_marketing_percentage?.toString() ?? ''}
                    />
                    <ReadOnlyTextInput
                        label="Joint marketing (NOK)"
                        value={term?.joint_marketing_bonus?.toString() ?? ''}
                    />
                </ProductFormSubsection>
                <ProductFormSubsection title="Collaboration bonus">
                    <ReadOnlyTextInput
                        label="Collaboration bonus (%)"
                        value={term?.collaboration_percentage?.toString() ?? ''}
                    />
                    <ReadOnlyTextInput
                        label="Collaboration (NOK)"
                        value={term?.collaboration_bonus?.toString() ?? ''}
                    />
                </ProductFormSubsection>
                <ProductFormSubsection title="Order size">
                    <ReadOnlyTextInput
                        label="Order size discount trigger"
                        value={term?.order_size_discount_trigger?.toString() ?? ''}
                    />
                    <ReadOnlyTextInput
                        label="Order size discount (%)"
                        value={term?.order_size_discount_percentage?.toString() ?? ''}
                    />
                </ProductFormSubsection>
            </ProductFormSection>
            <ComputedPricesTable consumerUnitPrice={consumerUnitPrice} purchasingUnitPrice={purchasingUnitPrice} />
        </>
    );
};
