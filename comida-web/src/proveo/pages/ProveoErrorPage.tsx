import { EuiPageTemplate } from '@elastic/eui';

import BaseProveoPage from './BaseProveoPage';

export const ProveoErrorPage: React.FC = () => {
    return (
        <>
            <BaseProveoPage panelled breadcrumbs={[]}>
                <EuiPageTemplate.EmptyPrompt
                    iconType="warning"
                    color="danger"
                    title={<h2>Something went wrong</h2>}
                    body={
                        <p>
                            Try refreshing the page, or check back in a little while. If this keeps happening, get in
                            touch with us and we’ll sort it out.
                        </p>
                    }
                />
            </BaseProveoPage>
        </>
    );
};
