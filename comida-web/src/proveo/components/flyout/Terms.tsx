import { EuiDatePicker, EuiSelect } from '@elastic/eui';
import moment from 'moment';
import { ChangeEventHandler } from 'react';

import { CurrencyInput, ProductFormRow, ProductFormSection } from '@/proveo/components/flyout/FormComponents';
import { Term } from '@/proveo/pages/PurchasePrices/api';
import { TermFormDecorator } from '@/proveo/shared/termDecorator';

export interface TermsSectionProps {
    term: TermFormDecorator;
    currencyCode: string;
    hasVariableWeight: boolean;
    handleTermValuesChange: (values: Partial<Term>) => void;
    handleEventChange: ChangeEventHandler<HTMLElement>;
}

export const TermsSection: React.FC<TermsSectionProps> = ({
    term,
    hasVariableWeight,
    currencyCode,
    handleTermValuesChange,
    handleEventChange,
}) => {
    const handleValidFromDateChange = (date: moment.Moment) => {
        handleTermValuesChange({ valid_from_date: date.format('YYYY-MM-DD') });
    };

    const pricingUnitOptions = [
        { value: 'FPK', text: 'FPK' },
        { value: 'DPK', text: 'DPK' },
    ];

    if (hasVariableWeight) {
        pricingUnitOptions.push({ value: 'KG', text: 'KG' });
    }

    return (
        <ProductFormSection title="Terms">
            <ProductFormRow label="Pricing Unit" helpText="Price level for the product">
                <EuiSelect
                    fullWidth
                    options={pricingUnitOptions}
                    value={term?.pricing_unit ?? 'FPK'}
                    onChange={(event) =>
                        handleTermValuesChange({
                            pricing_unit: event.target.value,
                        })
                    }
                />
            </ProductFormRow>
            <ProductFormRow label="Price valid from">
                <EuiDatePicker
                    selected={term?.valid_from_date ? moment(term?.valid_from_date) : null}
                    onChange={handleValidFromDateChange}
                    dateFormat="DD MMM YYYY"
                    minDate={moment(new Date())}
                    showTimeSelect={false}
                    shouldCloseOnSelect
                    fullWidth
                />
            </ProductFormRow>
            <CurrencyInput
                label="List price per pricing unit"
                name="list_price"
                value={term.list_price ?? undefined}
                onChange={handleEventChange}
            />
            <CurrencyInput
                label="Shipping cost"
                helpText="Only applicable if the supplier delivers the goods to us"
                name="supplier_shipping_cost"
                currencyCode={currencyCode}
                value={term?.supplier_shipping_cost ?? undefined}
                onChange={handleEventChange}
            />
        </ProductFormSection>
    );
};
