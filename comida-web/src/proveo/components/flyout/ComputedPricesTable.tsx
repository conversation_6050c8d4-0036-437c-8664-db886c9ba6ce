import { EuiBasicTable } from '@elastic/eui';

import { ComputedPrices } from '@/proveo/pages/PurchasePrices/api';

import { ProductFormSection } from './FormComponents';

interface ComputedPricesProps {
    consumerUnitPrice: ComputedPrices;
    purchasingUnitPrice: ComputedPrices;
}

export const ComputedPricesTable: React.FC<ComputedPricesProps> = ({ consumerUnitPrice, purchasingUnitPrice }) => {
    const columns = [
        { field: 'label', name: 'Calculated field' },
        { field: 'consumerUnit', name: 'Consumer unit (NOK)', align: 'right' as const },
        { field: 'purchasingUnit', name: 'Purchasing unit (NOK)', align: 'right' as const },
    ];

    const items = [
        {
            label: 'List price',
            consumerUnit: consumerUnitPrice.list_price,
            purchasingUnit: purchasingUnitPrice.list_price,
        },
        {
            label: '-Supplier discount',
            consumerUnit: consumerUnitPrice.supplier_discount,
            purchasingUnit: purchasingUnitPrice.supplier_discount,
        },
        {
            label: '-Other discount',
            consumerUnit: consumerUnitPrice.other_discount,
            purchasingUnit: purchasingUnitPrice.other_discount,
        },
        {
            label: '-Pallet discount',
            consumerUnit: consumerUnitPrice.pallet_discount,
            purchasingUnit: purchasingUnitPrice.pallet_discount,
        },
        {
            label: '+Shipping cost',
            consumerUnit: consumerUnitPrice.supplier_shipping_cost,
            purchasingUnit: purchasingUnitPrice.supplier_shipping_cost,
        },
        {
            label: 'Invoice price',
            consumerUnit: consumerUnitPrice.invoice_price,
            purchasingUnit: purchasingUnitPrice.invoice_price,
        },
        {
            label: '-Joint marketing',
            consumerUnit: consumerUnitPrice.joint_marketing_bonus,
            purchasingUnit: purchasingUnitPrice.joint_marketing_bonus,
        },
        {
            label: '-Collaboration bonus',
            consumerUnit: consumerUnitPrice.collaboration_bonus,
            purchasingUnit: purchasingUnitPrice.collaboration_bonus,
        },
        {
            label: '-Estimated kickback',
            consumerUnit: consumerUnitPrice.estimated_kickback,
            purchasingUnit: purchasingUnitPrice.estimated_kickback,
        },
        {
            label: 'Net purchase price',
            consumerUnit: consumerUnitPrice.net_purchase_price,
            purchasingUnit: purchasingUnitPrice.net_purchase_price,
        },
    ];

    return (
        <ProductFormSection title="Calculated fields">
            <p>Calculated based on the terms, discounts and number of consumer units in a purchasing unit</p>
            <EuiBasicTable columns={columns} items={items} />
        </ProductFormSection>
    );
};
