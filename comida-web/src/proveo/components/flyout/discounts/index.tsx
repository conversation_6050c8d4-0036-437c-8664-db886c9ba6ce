import { ChangeEventHandler } from 'react';

import { CurrencyInput, PercentageInput, ProductFormSection } from '@/proveo/components/flyout/FormComponents';
import { Term } from '@/proveo/pages/PurchasePrices/api';
import { TermFormDecorator } from '@/proveo/shared/termDecorator';

import { CollaborationBonusSection } from './CollaborationBonusSection';
import { JointMarketingSection } from './JointMarketingSection';
import { OrderSizeSection } from './OrderSizeSection';

export interface DiscountsSectionProps {
    term: TermFormDecorator;
    currencyCode: string;
    handleTermValuesChange: (values: Partial<Term>) => void;
    handleEventChange: ChangeEventHandler<HTMLElement>;
}

export const DiscountsSection: React.FC<DiscountsSectionProps> = ({
    term,
    currencyCode,
    handleTermValuesChange,
    handleEventChange,
}) => {
    return (
        <ProductFormSection title="Discounts">
            <PercentageInput
                label="Supplier discount (%)"
                name="supplier_discount_percentage"
                value={term?.supplier_discount_percentage ?? undefined}
                onChange={handleEventChange}
            />
            <CurrencyInput
                label="Supplier discount (NOK)"
                name="supplier_discount"
                value={term?.supplier_discount ?? undefined}
                onChange={handleEventChange}
            />
            <PercentageInput
                label="Other discount (%)"
                name="other_discount_percentage"
                value={term?.other_discount_percentage ?? undefined}
                onChange={handleEventChange}
            />
            <CurrencyInput
                label="Other discount (NOK)"
                name="other_discount"
                value={term?.other_discount ?? undefined}
                onChange={handleEventChange}
            />
            <CurrencyInput
                label="Discount tax"
                helpText="Only mandatory if joint marketing or collaboration bonus is calculated based on price excluding tax"
                name="discount_tax_amount"
                currencyCode={currencyCode}
                value={term?.discount_tax_amount ?? undefined}
                onChange={handleEventChange}
                disabled={!term.isEligibleForDiscountTax()}
            />

            <JointMarketingSection
                term={term}
                currencyCode={currencyCode}
                handleEventChange={handleEventChange}
                handleTermValuesChange={handleTermValuesChange}
            />
            <CollaborationBonusSection
                term={term}
                currencyCode={currencyCode}
                handleEventChange={handleEventChange}
                handleTermValuesChange={handleTermValuesChange}
            />
            <OrderSizeSection
                term={term}
                handleEventChange={handleEventChange}
                handleTermValuesChange={handleTermValuesChange}
            />
        </ProductFormSection>
    );
};
