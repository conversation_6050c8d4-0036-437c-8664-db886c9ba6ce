import { EuiSelect } from '@elastic/eui';

import {
    CurrencyInput,
    PercentageInput,
    ProductFormRow,
    ProductFormSubsection,
    stringToInt,
} from '@/proveo/components/flyout/FormComponents';
import { Term } from '@/proveo/pages/PurchasePrices/api';
import { TermFormDecorator } from '@/proveo/shared/termDecorator';
import { BonusType, jointMarketingBonusOptions } from '@/proveo/shared/types';

interface JointMarketingSectionProps {
    term: TermFormDecorator;
    currencyCode: string;
    handleTermValuesChange: (values: Partial<Term>) => void;
    handleEventChange: (event: React.ChangeEvent<any>) => void;
}

export const JointMarketingSection: ReactFC<JointMarketingSectionProps> = ({
    term,
    currencyCode,
    handleTermValuesChange,
    handleEventChange,
}) => {
    return (
        <ProductFormSubsection title="Joint marketing">
            <ProductFormRow label="Joint marketing calculation">
                <EuiSelect
                    fullWidth
                    defaultValue={undefined}
                    options={jointMarketingBonusOptions}
                    value={term?.joint_marketing_terms ?? 0}
                    onChange={(event) => {
                        const value = stringToInt(event.target.value);
                        if (!value) {
                            handleTermValuesChange({
                                joint_marketing_terms: 0,
                                joint_marketing_percentage: 0,
                                joint_marketing_bonus: 0,
                            });
                        }
                        if (value === BonusType.FixedAmount) {
                            handleTermValuesChange({
                                joint_marketing_terms: value,
                                joint_marketing_percentage: 0,
                            });
                        } else if (value && value > BonusType.FixedAmount && value <= BonusType.ListPriceExcTax) {
                            handleTermValuesChange({
                                joint_marketing_terms: value,
                                joint_marketing_bonus: 0,
                            });
                        }
                    }}
                />
            </ProductFormRow>
            <PercentageInput
                label="Joint marketing (%)"
                name="joint_marketing_percentage"
                value={term?.joint_marketing_percentage ?? undefined}
                onChange={handleEventChange}
                disabled={!term.hasPercentAmountJointMarketingBonus()}
            />
            <CurrencyInput
                label="Joint marketing (NOK)"
                name="joint_marketing_bonus"
                value={term?.joint_marketing_bonus ?? undefined}
                currencyCode={currencyCode}
                onChange={handleEventChange}
                disabled={!term.hasFixedAmountJointMarketingBonus()}
            />
        </ProductFormSubsection>
    );
};
