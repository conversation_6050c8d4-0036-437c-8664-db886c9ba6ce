import { EuiSelect } from '@elastic/eui';

import {
    PercentageInput,
    ProductFormRow,
    ProductFormSubsection,
    stringToInt,
} from '@/proveo/components/flyout/FormComponents';
import { Term } from '@/proveo/pages/PurchasePrices/api';
import { TermFormDecorator } from '@/proveo/shared/termDecorator';
import { orderDiscountOptions } from '@/proveo/shared/types';

interface OrderSizeSectionProps {
    term: TermFormDecorator;
    handleTermValuesChange: (values: Partial<Term>) => void;
    handleEventChange: (event: React.ChangeEvent<any>) => void;
}

export const OrderSizeSection: ReactFC<OrderSizeSectionProps> = ({
    term,
    handleTermValuesChange,
    handleEventChange,
}) => {
    return (
        <ProductFormSubsection title="Order size">
            <ProductFormRow label="Order size discount trigger">
                <EuiSelect
                    fullWidth
                    options={orderDiscountOptions}
                    value={term?.order_size_discount_trigger ?? 0}
                    onChange={(event) =>
                        handleTermValuesChange({
                            order_size_discount_trigger: stringToInt(event.target.value),
                        })
                    }
                />
            </ProductFormRow>
            <PercentageInput
                label="Order size discount (%)"
                name="order_size_discount_percentage"
                value={term?.order_size_discount_percentage ?? undefined}
                onChange={handleEventChange}
                disabled={!term.isEligibleForOrderSizeDiscount()}
            />
        </ProductFormSubsection>
    );
};
