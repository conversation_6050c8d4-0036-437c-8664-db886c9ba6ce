import { EuiSelect } from '@elastic/eui';

import {
    CurrencyInput,
    PercentageInput,
    ProductFormRow,
    ProductFormSubsection,
    stringToInt,
} from '@/proveo/components/flyout/FormComponents';
import { Term } from '@/proveo/pages/PurchasePrices/api';
import { TermFormDecorator } from '@/proveo/shared/termDecorator';
import { BonusType, collaborationBonusOptions } from '@/proveo/shared/types';

export interface CollaborationBonusSectionProps {
    term: TermFormDecorator;
    currencyCode: string;
    handleTermValuesChange: (values: Partial<Term>) => void;
    handleEventChange: (event: React.ChangeEvent<any>) => void;
}

export const CollaborationBonusSection: ReactFC<CollaborationBonusSectionProps> = ({
    term,
    currencyCode,
    handleTermValuesChange,
    handleEventChange,
}) => {
    return (
        <ProductFormSubsection title="Collaboration bonus">
            <ProductFormRow label="Collaboration bonus calculation">
                <EuiSelect
                    fullWidth
                    defaultValue={undefined}
                    options={collaborationBonusOptions}
                    value={term?.collaboration_terms ?? 0}
                    onChange={(event) => {
                        const value = stringToInt(event.target.value);
                        if (!value) {
                            handleTermValuesChange({
                                collaboration_terms: 0,
                                collaboration_percentage: 0,
                                collaboration_bonus: 0,
                            });
                        }
                        if (value === BonusType.FixedAmount) {
                            handleTermValuesChange({
                                collaboration_terms: value,
                                collaboration_percentage: 0,
                            });
                        } else if (value && value > BonusType.FixedAmount && value <= BonusType.ListPriceExcTax) {
                            handleTermValuesChange({
                                collaboration_terms: value,
                                collaboration_bonus: 0,
                            });
                        }
                    }}
                />
            </ProductFormRow>
            <PercentageInput
                label="Collaboration bonus (%)"
                name="collaboration_percentage"
                value={term?.collaboration_percentage ?? undefined}
                onChange={handleEventChange}
                disabled={!term.hasPercentAmountCollaborationBonus()}
            />
            <CurrencyInput
                label="Collaboration (NOK)"
                name="collaboration_bonus"
                value={term?.collaboration_bonus ?? undefined}
                currencyCode={currencyCode}
                onChange={handleEventChange}
                disabled={!term.hasFixedAmountCollaborationBonus()}
            />
        </ProductFormSubsection>
    );
};
