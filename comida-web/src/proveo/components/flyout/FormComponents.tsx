import {
    EuiDescriptionList,
    EuiFieldText,
    EuiFieldTextProps,
    EuiFlexGroup,
    EuiFlexItem,
    EuiFormLabel,
    EuiFormRow,
    EuiFormRowProps,
    EuiSpacer,
    EuiText,
    EuiTitle,
} from '@elastic/eui';
import { ComponentPropsWithoutRef, ReactNode } from 'react';
import { NumericFormat } from 'react-number-format';

export function ProductFormRow(props: EuiFormRowProps) {
    return (
        <EuiFlexItem>
            <EuiFormRow display="columnCompressed" fullWidth {...props} />
        </EuiFlexItem>
    );
}

export function ProductFormSection({
    title,
    description,
    children,
}: {
    title: string;
    description?: ReactNode;
    children: ReactNode;
}) {
    return (
        <EuiFlexItem>
            <EuiSpacer size="m" />
            <EuiTitle size="s">
                <h3>{title}</h3>
            </EuiTitle>
            {description && (
                <>
                    <EuiSpacer size="s" />
                    <EuiText size="s">{description}</EuiText>
                </>
            )}
            <EuiSpacer size="m" />
            <EuiFlexGroup gutterSize="m" direction="column">
                {children}
            </EuiFlexGroup>
        </EuiFlexItem>
    );
}

export function ProductFormSubsection({ title, children }: { title: string; children: ReactNode }) {
    return (
        <>
            <EuiTitle size="xs">
                <h4>{title}</h4>
            </EuiTitle>
            {children}
            <EuiSpacer size="s" />
        </>
    );
}

interface LabelledFieldProps {
    label: string;
    helpText?: string;
    thousandSeparator?: boolean;
    decimalScale?: number;
    fixedDecimalScale?: boolean;
    suffixValue?: string;
    onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

export const NumericInput: ReactFC<EuiFieldTextProps & LabelledFieldProps> = (props) => {
    const { suffixValue, onChange, helpText, ...rest } = props;

    const append = suffixValue ? <EuiFormLabel>{suffixValue}</EuiFormLabel> : undefined;

    return (
        <ProductFormRow label={props.label} helpText={helpText}>
            <NumericFormat<Omit<ComponentPropsWithoutRef<'input'>, 'defaultValue' | 'value' | 'children'>>
                fullWidth
                customInput={EuiFieldText}
                {...(rest as any)}
                append={append}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    const newEvent = {
                        ...e,
                        target: {
                            ...e.target,
                            name: e.target.name,
                            value: e.target.value.replace(/,/g, ''),
                        },
                    };
                    if (onChange) onChange(newEvent);
                }}
            />
        </ProductFormRow>
    );
};

export function ProductInfoFlexItem({
    title,
    children,
}: {
    title: NonNullable<ReactNode>;
    children: NonNullable<ReactNode>;
}) {
    return (
        <EuiFlexItem>
            <EuiDescriptionList
                compressed
                listItems={[
                    {
                        title,
                        description: children,
                    },
                ]}
            />
        </EuiFlexItem>
    );
}
export const TextInput: ReactFC<{
    label: string;
    name: string;
    value?: string;
    onChange: any;
    error?: string;
    required?: boolean;
}> = ({ label, name, value, onChange, error, required = false }) => (
    <ProductFormRow
        label={required ? `${label} *` : label}
        fullWidth
        display="columnCompressed"
        isInvalid={!!error}
        error={error}
    >
        <EuiFieldText compressed aria-label={label} name={name} value={value} onChange={onChange} isInvalid={!!error} />
    </ProductFormRow>
);

export const ReadOnlyTextInput: ReactFC<{ label: string; value?: string }> = ({ label, value }) => (
    <ProductFormRow label={label} fullWidth display="columnCompressed">
        <EuiFieldText readOnly compressed aria-label={label} value={value} />
    </ProductFormRow>
);

type CurrencyInputProps = EuiFieldTextProps & LabelledFieldProps & { currencyCode?: string };

export const CurrencyInput: ReactFC<CurrencyInputProps> = (props) => {
    const { currencyCode = 'NOK', ...rest } = props;
    return <NumericInput decimalScale={8} suffixValue={currencyCode} {...(rest as any)} />;
};

type PercentageInputProps = EuiFieldTextProps & LabelledFieldProps;

export const PercentageInput: ReactFC<PercentageInputProps> = (props) => {
    return <NumericInput decimalScale={8} suffixValue="%" {...(props as any)} />;
};

export const ReadOnlyCurrencyValue: ReactFC<{ label: string; value?: number }> = ({ label, value }) => (
    <CurrencyInput label={label} value={value} readOnly />
);

export function stringToInt(value: string): number | undefined {
    const intValue = parseInt(value, 10);
    if (Number.isNaN(intValue) || intValue === 0) {
        return undefined;
    }
    return intValue;
}
