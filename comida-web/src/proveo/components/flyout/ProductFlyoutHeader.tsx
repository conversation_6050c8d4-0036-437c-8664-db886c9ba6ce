import { EuiFlexGroup, EuiFlexItem, EuiImage, EuiTitle } from '@elastic/eui';

import ProductIcon from '@/icons/product.png';
import { useProveoUser } from '@/proveo/hooks';
import { useProductImage } from '@/proveo/pages/PurchasePrices/priceChangesQueries';

interface ProductFlyoutHeaderProps {
    gtin: string;
    name: string;
    statusBadge?: React.ReactNode;
}

export const ProductFlyoutHeader: React.FC<ProductFlyoutHeaderProps> = ({ gtin, name, statusBadge }) => {
    const user = useProveoUser();
    const { data } = useProductImage({
        target_market: user?.country ?? '',
        gtin,
    });

    const imageUrl = data?.product_image_thumbnail_url ?? ProductIcon;

    return (
        <EuiFlexGroup alignItems="center">
            <EuiFlexItem grow={false}>
                <EuiImage alt="Product image" allowFullScreen src={imageUrl} size={64} />
            </EuiFlexItem>
            <EuiFlexItem grow={false}>
                <EuiFlexGroup direction="column" gutterSize="s">
                    <EuiFlexItem grow={false}>
                        <EuiTitle size="m">
                            <h1>{name}</h1>
                        </EuiTitle>
                    </EuiFlexItem>
                    {statusBadge && <EuiFlexGroup>{statusBadge}</EuiFlexGroup>}
                </EuiFlexGroup>
            </EuiFlexItem>
        </EuiFlexGroup>
    );
};
