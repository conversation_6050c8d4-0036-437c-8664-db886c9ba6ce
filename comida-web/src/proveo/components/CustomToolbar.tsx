import { EuiDataGridCustomToolbarProps, EuiFlexGroup, EuiFlexItem } from '@elastic/eui';
import { css } from '@emotion/react';

interface CustomToolbarProps extends EuiDataGridCustomToolbarProps {
    leftControls: React.ReactNode;
    rightControls: React.ReactNode;
}

export const CustomToolbar: React.FC<CustomToolbarProps> = ({
    leftControls,
    rightControls,
    columnControl,
    columnSortingControl,
    displayControl,
    fullScreenControl,
    keyboardShortcutsControl,
}) => {
    return (
        <EuiFlexGroup
            gutterSize="xs"
            justifyContent="spaceBetween"
            alignItems="center"
            className="euiDataGrid__controls"
            css={css`
                padding: 8px;
            `}
        >
            <EuiFlexItem grow={false}>{leftControls}</EuiFlexItem>
            <EuiFlexItem grow={false}>
                <EuiFlexGroup responsive={false} gutterSize="s" alignItems="center">
                    {rightControls}
                    <EuiFlexItem grow={false}>{columnControl}</EuiFlexItem>
                    <EuiFlexItem grow={false}>{columnSortingControl}</EuiFlexItem>
                    <EuiFlexItem grow={false}>{displayControl}</EuiFlexItem>
                    <EuiFlexItem grow={false}>{fullScreenControl}</EuiFlexItem>
                    <EuiFlexItem grow={false}>{keyboardShortcutsControl}</EuiFlexItem>
                </EuiFlexGroup>
            </EuiFlexItem>
        </EuiFlexGroup>
    );
};
