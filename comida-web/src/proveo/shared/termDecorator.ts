import { Term } from '@/proveo/pages/PurchasePrices/api';

import { BonusType, OrderDiscountType } from './types';

export interface TermFormDecorator extends Term {
    isEligibleForOrderSizeDiscount(): boolean;
    isEligibleForDiscountTax(): boolean;
    hasFixedAmountJointMarketingBonus(): boolean;
    hasPercentAmountJointMarketingBonus(): boolean;
    hasFixedAmountCollaborationBonus(): boolean;
    hasPercentAmountCollaborationBonus(): boolean;
}

export function decorateTermWithCalcFunctions(row: Term): TermFormDecorator {
    return {
        ...row,
        isEligibleForOrderSizeDiscount() {
            return [OrderDiscountType.PalletAlways, OrderDiscountType.QuantityTrigger].includes(
                row?.order_size_discount_trigger ?? OrderDiscountType.None,
            );
        },
        isEligibleForDiscountTax() {
            return (
                [BonusType.NetInvoiceExcTax, BonusType.ListPriceExcTax].includes(
                    row?.collaboration_terms ?? BonusType.None,
                ) ||
                [BonusType.NetInvoiceExcTax, BonusType.ListPriceExcTax].includes(
                    row?.joint_marketing_terms ?? BonusType.None,
                )
            );
        },
        hasFixedAmountJointMarketingBonus() {
            return row?.joint_marketing_terms === BonusType.FixedAmount;
        },
        hasPercentAmountJointMarketingBonus() {
            return [
                BonusType.NetInvoiceIncTax,
                BonusType.NetInvoiceExcTax,
                BonusType.ListPriceIncTax,
                BonusType.ListPriceExcTax,
            ].includes(row?.joint_marketing_terms ?? BonusType.None);
        },
        hasFixedAmountCollaborationBonus() {
            return row?.collaboration_terms === BonusType.FixedAmount;
        },
        hasPercentAmountCollaborationBonus() {
            return [
                BonusType.NetInvoiceIncTax,
                BonusType.NetInvoiceExcTax,
                BonusType.ListPriceIncTax,
                BonusType.ListPriceExcTax,
            ].includes(row?.collaboration_terms ?? BonusType.None);
        },
    };
}
