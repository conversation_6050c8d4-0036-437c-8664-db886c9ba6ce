export enum OrderDiscountType {
    None = 0,
    PalletAlways = 1,
    QuantityTrigger = 2,
}

export const orderDiscountOptions = [
    { value: OrderDiscountType.None, text: 'No discount' },
    { value: OrderDiscountType.PalletAlways, text: 'Pallet discount included on Orderable Unit GTIN' },
    {
        value: OrderDiscountType.QuantityTrigger,
        text: 'Triggered by ordering a quantity fitting the pallet in the same EPD',
    },
];

export enum BonusType {
    None = 0,
    FixedAmount = 1,
    NetInvoiceIncTax = 2,
    NetInvoiceExcTax = 3,
    ListPriceIncTax = 4,
    ListPriceExcTax = 5,
}

export const jointMarketingBonusOptions = [
    { value: BonusType.None, text: 'No joint marketing' },
    { value: BonusType.FixedAmount, text: 'Fixed amount' },
    { value: BonusType.NetInvoiceIncTax, text: '% of net invoice price (including tax)' },
    { value: BonusType.NetInvoiceExcTax, text: '% of net invoice price (excluding tax)' },
    { value: BonusType.ListPriceIncTax, text: '% of list price (including tax)' },
    { value: BonusType.ListPriceExcTax, text: '% of list price (excluding tax)' },
];

export const collaborationBonusOptions = [
    { value: BonusType.None, text: 'No collaboration bonus' },
    { value: BonusType.FixedAmount, text: 'Fixed amount' },
    { value: BonusType.NetInvoiceIncTax, text: '% of net invoice price (including tax)' },
    { value: BonusType.NetInvoiceExcTax, text: '% of net invoice price (excluding tax)' },
    { value: BonusType.ListPriceIncTax, text: '% of list price (including tax)' },
    { value: BonusType.ListPriceExcTax, text: '% of list price (excluding tax)' },
];

export interface FixedPrices {
    listPrice: number;
    jointMarketingBonus: number;
    collaborationBonus: number;
    discountTaxAmount: number;
    estimatedKickback: number;
}

export interface CalculatedFields {
    listPrice: () => number;
    supplierDiscount: () => number;
    otherDiscount: () => number;
    palletDiscount: () => number;
    invoicePrice: () => number;
    jointMarketingBonus: () => number;
    collaborationBonus: () => number;
    netPurchasePrice: () => number;
    estimatedKickback: () => number;
}

export interface BonusCalculationParams {
    listPrice: Maybe<number>;
    invoicePrice: Maybe<number>;
    percentage: Maybe<number>;
    fixedAmount: Maybe<number>;
    discountTaxAmount: Maybe<number>;
}
