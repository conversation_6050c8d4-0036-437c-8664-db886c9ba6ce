import { EuiBadge } from '@elastic/eui';

import { TradeItemListRecord } from '@/piscina/api';
import { getColorForBadge } from '@/piscina/euiComponents/utils';

interface Props {
    item: TradeItemListRecord;
}

export default function StatusBadge({ item }: Props) {
    const isActive = item.is_active || false;

    return (
        <EuiBadge color={isActive ? getColorForBadge('success') : 'default'}>
            {isActive ? 'Active' : 'Inactive'}
        </EuiBadge>
    );
}
