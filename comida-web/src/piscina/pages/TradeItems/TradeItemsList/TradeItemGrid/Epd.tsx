import { EuiText } from '@elastic/eui';
import { useState } from 'react';

import { TradeItemListRecord } from '@/piscina/api';

interface Props {
    item: TradeItemListRecord;
}

export default function TradeItemEpd({ item }: Props) {
    const identifiers = item.identifier_module?.identifiers || [];
    const [displayedCount, setDisplayedCount] = useState(2);

    const hasMore = displayedCount < identifiers.length;

    return (
        <div onMouseEnter={() => setDisplayedCount(identifiers.length)} onMouseLeave={() => setDisplayedCount(2)}>
            {identifiers.length === 0
                ? 'N/A'
                : identifiers
                      .filter((_, index) => index < displayedCount)
                      .map((identifier) => (
                          <EuiText size={hasMore ? 'xs' : 's'} key={identifier.identifier_value}>
                              {identifier.identifier_value}
                          </EuiText>
                      ))}

            {hasMore && (
                <EuiText size="xs" style={{ color: '#98a2b3' }}>
                    +{identifiers.length - displayedCount} more
                </EuiText>
            )}
        </div>
    );
}
