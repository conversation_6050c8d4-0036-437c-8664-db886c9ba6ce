import { Criteria, EuiBasicTable, EuiFlexGroup, EuiLoadingSpinner, useIsWithinMaxBreakpoint } from '@elastic/eui';
import { css } from '@emotion/react';

import { TradeItemListRecord } from '@/piscina/api';
import { useLoggedInUser } from '@/Providers/UserContext';

import { PAGINATION_SIZE_OPTIONS } from '../constants';
import { PaginationState } from '../types';
import useColumns from './useColumns';

interface TradeItemGridProps {
    tradeItemList: TradeItemListRecord[];
    totalItems: number;
    loading: boolean;
    pageIndex: number;
    pageSize: number;
    onPaginationChange: (newPagination: PaginationState) => void;
}

export default function TradeItemGrid({
    tradeItemList,
    totalItems,
    loading,
    pageIndex,
    pageSize,
    onPaginationChange,
}: TradeItemGridProps) {
    const user = useLoggedInUser();
    const isMobile = useIsWithinMaxBreakpoint('s'); // Adjust layout based on screen size

    const columns = useColumns();

    const onRowClick = (item: TradeItemListRecord) => {
        window.open(`/products/${user.country}/${item.gtin}`, '_blank');
    };

    const onTableChange = ({ page }: Criteria<TradeItemListRecord>) => {
        if (page) {
            const { index, size } = page;
            onPaginationChange({ pageIndex: index, pageSize: size });
        }
    };

    return (
        <EuiBasicTable
            aria-label="Trade Item List"
            css={
                // Styles to make the table fit the screen's height and have a sticky header and pagination
                css`
                    height: calc(100vh - 210px);
                    overflow: overlay;

                    & > table {
                        overflow: auto;

                        thead {
                            position: sticky;
                            top: 0px;
                            z-index: 2;
                            background-color: white;
                            border-bottom: 2px solid #d3dae6;
                        }
                    }

                    & > div:last-child {
                        position: sticky;
                        bottom: 0;
                        background-color: white;
                        z-index: 2;
                        padding-bottom: 8px;
                    }
                `
            }
            items={tradeItemList}
            columns={columns}
            pagination={{
                pageIndex,
                pageSize,
                pageSizeOptions: PAGINATION_SIZE_OPTIONS,
                totalItemCount: totalItems,
            }}
            onChange={onTableChange}
            rowProps={(item) => ({
                onClick: () => onRowClick(item),
                style: isMobile ? undefined : { height: '64px' },
            })}
            loading={loading}
            noItemsMessage={
                loading ? (
                    <EuiFlexGroup alignItems="center" gutterSize="m">
                        <EuiLoadingSpinner size="m" /> Loading trade items...
                    </EuiFlexGroup>
                ) : (
                    'No trade items found'
                )
            }
        />
    );
}
