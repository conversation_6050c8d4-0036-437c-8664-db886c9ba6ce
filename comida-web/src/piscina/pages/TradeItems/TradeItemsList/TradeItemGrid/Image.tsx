import { EuiImage } from '@elastic/eui';

import { TradeItemListRecord } from '@/piscina/api';
import { DefaultImagesState } from '@/piscina/features/defaultImages/defaultImagesSlice';

interface Props {
    item: TradeItemListRecord;
    defaultImages: DefaultImagesState;
}

export default function Image({ item, defaultImages }: Props) {
    const primaryImage = item.asset_module?.product_images.find((image) => image.is_primary_file);
    const url = primaryImage?.product_image_url || defaultImages?.product?.product_image_url;

    if (!url) return null;

    return (
        <EuiImage
            alt="Trade item image"
            url={url}
            style={{ objectFit: 'contain', maxWidth: '48px', maxHeight: '48px' }}
        />
    );
}
