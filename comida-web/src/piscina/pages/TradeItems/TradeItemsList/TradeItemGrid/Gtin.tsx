import { EuiFlexGroup, EuiFlexItem } from '@elastic/eui';

import { TradeItemListRecord } from '@/piscina/api';

interface Props {
    item: TradeItemListRecord;
}

export default function TradeItemGtin({ item }: Props) {
    const { gtin } = item;

    return (
        <EuiFlexGroup justifyContent="flexEnd" alignItems="center" style={{ height: '100%' }}>
            <EuiFlexItem grow={false}>
                <div>{gtin}</div>
            </EuiFlexItem>
        </EuiFlexGroup>
    );
}
