import { EuiBasicTableColumn } from '@elastic/eui';

import { useAppSelector } from '@/app/hooks';
import { TradeItemListRecord } from '@/piscina/api';

import Epd from './Epd';
import Flags from './Flags';
import Gtin from './Gtin';
import Image from './Image';
import Name from './Name';
import StatusBadge from './StatusBadge';

export default function useColumns(): EuiBasicTableColumn<TradeItemListRecord>[] {
    const defaultImages = useAppSelector((state) => state.defaultImages);

    return [
        {
            id: 'image',
            name: ' ',
            align: 'center',
            width: '100px',
            render: (item: TradeItemListRecord) => <Image item={item} defaultImages={defaultImages} />,
        },
        {
            id: 'name',
            name: 'Trade item name',
            render: (item: TradeItemListRecord) => <Name item={item} />,
        },
        {
            id: 'gtin',
            name: 'GTIN',
            width: '170px',
            align: 'right',
            render: (item: TradeItemListRecord) => <Gtin item={item} />,
        },
        {
            id: 'epd',
            name: 'EPD',
            width: '130px',
            align: 'right',
            render: (item: TradeItemListRecord) => <Epd item={item} />,
        },
        {
            id: 'flags',
            name: 'Flags',
            width: '130px',
            align: 'center',
            render: (item: TradeItemListRecord) => <Flags item={item} />,
        },
        {
            id: 'status',
            name: 'Status',
            width: '120px',
            render: (item: TradeItemListRecord) => <StatusBadge item={item} />,
        },
    ];
}
