import { EuiFlexGroup, EuiNotificationBadge } from '@elastic/eui';

import { TradeItemListRecord } from '@/piscina/api';

interface Props {
    item: TradeItemListRecord;
}

export default function Flags({ item }: Props) {
    const errorCount = item.error_count || 0;
    const reviewItemCount = item.review_item_count || 0;

    if (!errorCount && !reviewItemCount) {
        return null;
    }

    return (
        <EuiFlexGroup alignItems="center" justifyContent="center" gutterSize="s">
            {errorCount > 0 && <EuiNotificationBadge color="accent">{errorCount}</EuiNotificationBadge>}

            {reviewItemCount > 0 && (
                <EuiNotificationBadge style={{ backgroundColor: '#F2BC2C', color: 'black' }}>
                    {reviewItemCount}
                </EuiNotificationBadge>
            )}
        </EuiFlexGroup>
    );
}
