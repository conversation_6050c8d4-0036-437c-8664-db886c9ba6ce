import { EuiTextBlockTruncate } from '@elastic/eui';

import { TradeItemListRecord } from '@/piscina/api';

interface Props {
    item: TradeItemListRecord;
}

export default function Name({ item }: Props) {
    const shortDescription = item.description_module?.descriptions[0]?.short_description || '';

    return (
        <EuiTextBlockTruncate lines={2} style={{ width: '100%' }}>
            {shortDescription}
        </EuiTextBlockTruncate>
    );
}
